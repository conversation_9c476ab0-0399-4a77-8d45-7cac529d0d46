#!/usr/bin/env python3
"""
从16ShipCollection.png中提取适合的飞机图片，用于替换坦克图片
"""

from PIL import Image
import os

def extract_aircraft_sprites():
    """从飞机素材集合中提取适合的飞机图片"""
    
    # 打开原始图片
    source_img = Image.open('16ShipCollection.png')
    print(f"原始图片尺寸: {source_img.size}")
    
    # 创建输出目录
    output_dir = 'extracted_aircraft'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 飞机素材通常是16x16的网格排列
    # 我们需要手动选择一些合适的飞机
    
    # 假设图片是按网格排列的，每个飞机16x16像素
    sprite_width = 16
    sprite_height = 16
    
    # 计算网格数量
    cols = source_img.width // sprite_width
    rows = source_img.height // sprite_height
    
    print(f"网格尺寸: {cols} x {rows}")
    
    # 选择一些合适的飞机位置（需要根据实际图片调整）
    # 这里我先提取前几行的飞机作为示例
    selected_positions = [
        # 玩家飞机 - 4个等级，每个等级2帧
        (0, 0),   # m0-0-1.png
        (1, 0),   # m0-0-2.png
        (2, 0),   # m1-0-1.png
        (3, 0),   # m1-0-2.png
        (4, 0),   # m2-0-1.png
        (5, 0),   # m2-0-2.png
        (6, 0),   # m3-0-1.png
        (7, 0),   # m3-0-2.png
    ]
    
    # 对应的文件名
    filenames = [
        'm0-0-1.png',
        'm0-0-2.png', 
        'm1-0-1.png',
        'm1-0-2.png',
        'm2-0-1.png',
        'm2-0-2.png',
        'm3-0-1.png',
        'm3-0-2.png'
    ]
    
    # 提取飞机图片
    for i, (col, row) in enumerate(selected_positions):
        if i >= len(filenames):
            break
            
        # 计算裁剪区域
        left = col * sprite_width
        top = row * sprite_height
        right = left + sprite_width
        bottom = top + sprite_height
        
        # 裁剪图片
        sprite = source_img.crop((left, top, right, bottom))
        
        # 保存图片
        output_path = os.path.join(output_dir, filenames[i])
        sprite.save(output_path)
        print(f"已保存: {output_path}")
    
    print(f"\n提取完成！共提取了 {len(filenames)} 张飞机图片")
    print(f"图片保存在: {output_dir} 目录中")

def create_simple_aircraft():
    """创建简单的像素艺术飞机图片"""
    
    output_dir = 'simple_aircraft'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 创建16x16的飞机图片
    aircraft_designs = [
        # 基础飞机设计 (m0)
        [
            "0000000110000000",
            "0000001111000000", 
            "0000011111100000",
            "0001111111110000",
            "0011111111111000",
            "0111111111111100",
            "1111111111111110",
            "1111111111111110",
            "1111111111111110",
            "0111111111111100",
            "0011111111111000",
            "0001111111110000",
            "0000011111100000",
            "0000001111000000",
            "0000000110000000",
            "0000000000000000"
        ],
        # 升级飞机设计 (m1)
        [
            "0000000110000000",
            "0000001111000000",
            "0000011111100000", 
            "0001111111110000",
            "0011111111111000",
            "0111111111111100",
            "1111111111111110",
            "1111111111111110",
            "1111111111111110",
            "0111111111111100",
            "0011111111111000",
            "0001111111110000",
            "0000011111100000",
            "0000001111000000",
            "0000000110000000",
            "0000000000000000"
        ]
    ]
    
    # 颜色定义 (RGBA)
    colors = {
        '0': (0, 0, 0, 0),      # 透明
        '1': (100, 100, 100, 255),  # 灰色飞机
        '2': (150, 150, 150, 255),  # 亮灰色
        '3': (200, 200, 200, 255),  # 更亮灰色
    }
    
    filenames = [
        'm0-0-1.png', 'm0-0-2.png',
        'm1-0-1.png', 'm1-0-2.png', 
        'm2-0-1.png', 'm2-0-2.png',
        'm3-0-1.png', 'm3-0-2.png'
    ]
    
    # 为每个等级创建两帧动画
    for level in range(4):  # 4个等级
        for frame in range(2):  # 2帧动画
            img = Image.new('RGBA', (16, 16), (0, 0, 0, 0))
            pixels = img.load()
            
            # 使用基础设计，但根据等级调整颜色
            design = aircraft_designs[0]  # 使用基础设计
            
            for y in range(16):
                for x in range(16):
                    if y < len(design) and x < len(design[y]):
                        pixel_char = design[y][x]
                        if pixel_char == '1':
                            # 根据等级和帧数调整颜色
                            if level == 0:
                                color = (80, 80, 80, 255)   # 深灰
                            elif level == 1:
                                color = (120, 120, 120, 255) # 中灰
                            elif level == 2:
                                color = (160, 160, 160, 255) # 亮灰
                            else:  # level 3
                                color = (200, 200, 200, 255) # 最亮
                            
                            # 第二帧稍微调整亮度（动画效果）
                            if frame == 1:
                                color = tuple(min(255, c + 20) for c in color[:3]) + (255,)
                            
                            pixels[x, y] = color
                        else:
                            pixels[x, y] = (0, 0, 0, 0)  # 透明
            
            # 保存图片
            filename = filenames[level * 2 + frame]
            output_path = os.path.join(output_dir, filename)
            img.save(output_path)
            print(f"已创建: {output_path}")
    
    print(f"\n简单飞机图片创建完成！")
    print(f"图片保存在: {output_dir} 目录中")

if __name__ == "__main__":
    print("=== 飞机图片提取工具 ===\n")
    
    # 检查原始图片是否存在
    if os.path.exists('16ShipCollection.png'):
        print("1. 从素材集合中提取飞机图片...")
        try:
            extract_aircraft_sprites()
        except Exception as e:
            print(f"提取失败: {e}")
            print("将创建简单的飞机图片...")
            create_simple_aircraft()
    else:
        print("未找到16ShipCollection.png，创建简单的飞机图片...")
        create_simple_aircraft()
    
    print("\n=== 完成 ===")
