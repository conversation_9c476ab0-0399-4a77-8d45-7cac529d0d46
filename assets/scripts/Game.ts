import AudioMgr from "./AudioMgr";
import MapLayer from "./MapLayer";
import UpdateInfomations from "./UpdateInfomations"
import PlayerTank from "./PlayerTank";

const {ccclass, property} = cc._decorator;

@ccclass
export default class Game extends cc.Component {
    @property(cc.Prefab)
    black: cc.Prefab = null;

    @property
    private _level: number = 1;

    @property({
        type: cc.Integer,
        min: 1,
        max: 35
    })

    get level(): number {
        return this._level;
    }

    set level(v: number) {
        if (v > 35) {
            v -= 35;
        }
        this._level = v;
    }

    gameStart() {
        // 播放开始游戏音效
        cc.find("/Game/AudioMgr").getComponent(AudioMgr).playAudio("game_start");

        // 隐藏GameLayer和StageLayer
        cc.find("/Canvas/GameLayer").active = false;
        cc.find("/Canvas/StageLayer").active = false;

        // 设置相机背景色
        cc.find("Canvas/Main Camera").getComponent(cc.Camera).backgroundColor = new cc.Color(100, 100, 100);

        // 展示开场动画
        this.showAnimation();
    }

    gameOver() {
        let visableSize = cc.view.getVisibleSize();
 
        let gameOverNode = cc.find("/Canvas/External/gameover_up");
        gameOverNode.setPosition(0, -visableSize.height / 2 - gameOverNode.height / 2);
 
        cc.tween(gameOverNode)
            .to(1.5, { position: cc.v3(0, 0) })
            .delay(0.5)
            .call(() => {
                // 玩家停止移动
                let player = cc.find("/Canvas/GameLayer/MapLayer/player").children[0].getComponent(PlayerTank);
                player.moveStop();

                // 播放失败音效
                cc.find("/Game/AudioMgr").getComponent(AudioMgr).playAudio("game_over");
 
                // 禁用其他节点
                cc.find("/Canvas/GameLayer").active = false;
                cc.find("/Canvas/External/gameover_left").active = false;
                cc.find("/Canvas/External/gameover_up").active = false;
 
                // 切换到Game Over
                cc.find("/Canvas/Main Camera").getComponent(cc.Camera).backgroundColor
                    = new cc.Color(0, 0, 0);
                let bigGameOVer = cc.find("/Canvas/External/big_gameover");
                bigGameOVer.setPosition(0, 0);

                // 3s后回到menu界面
                this.scheduleOnce(() => {
                    cc.director.loadScene("Menu");
                }, 3);
            })
            .start();
    }

    showAnimation() {
        let visableSize = cc.view.getVisibleSize();

        let blackUp = cc.instantiate(this.black);
        blackUp.width = visableSize.width;
        blackUp.setPosition(0, visableSize.height/2 + blackUp.height/2);

        let blackDown = cc.instantiate(this.black);
        blackDown.width = visableSize.width;
        blackDown.setPosition(0, -(visableSize.height/2 + blackDown.height/2));

        let canvas = cc.find("/Canvas");
        blackUp.parent = canvas;
        blackDown.parent = canvas;

        cc.tween(blackUp).to(1, {position: cc.v3(0, visableSize.height/4)})
            .call(() => {
                blackUp.destroy();
            })
            .start();

        cc.tween(blackDown).to(1, {position: cc.v3(0, -visableSize.height/4)})
            .call(() => {
                blackDown.destroy();

                // 展示stage
                this.showStage();
            })
            .delay(1)
            .start();
    }

    showStage() {
        // 激活StageLayer
        let stageLayer = cc.find("/Canvas/StageLayer");
        stageLayer.active = true;
        stageLayer.getChildByName("level").getComponent(cc.Label).string = this._level.toString();

        //1s后执行
        this.scheduleOnce(() => {
            // 关闭StageLayer
            stageLayer.active = false;

            // 激活GameLayer
            let gameLayer = cc.find("/Canvas/GameLayer");
            gameLayer.active = true;

            // 初始化游戏界面
            let mapLayer = gameLayer.getChildByName("MapLayer").getComponent(MapLayer);
            let infomations = gameLayer.getChildByName("Infomations").getComponent(UpdateInfomations);
            infomations.init(this._level);
            mapLayer.init();
        }, 1);
    }

    onLoad () {
        cc.director.preloadScene("Menu");
        this.gameStart();
    }
}
