	
import { Globals, Dir } from "./Global";
import BaseTank from "./BaseTank";
import MapLayer from "./MapLayer";
import EnemyTank from "./EnemyTank";
import BlockWall from "./BlockWall";
import BlockCamp from "./BlockCamp";
import PlayerTank from "./PlayerTank";
import AudioMgr from "./AudioMgr";
import BlockStone from "./BlockStone";
import { log_debug } from "./Utils";

const {ccclass, property} = cc._decorator;

@ccclass
export default class Bullet extends cc.Component {

    @property(cc.SpriteFrame)
    frames: cc.SpriteFrame[] = [];

    tank: BaseTank;
    isEnemy: boolean;
    dir: number;
    step: number;
    stopMoving: boolean;
    mapLayer: MapLayer;

    init(dir: Dir, pos: cc.Vec3, step: number, tank: BaseTank) {
        this.mapLayer = cc.find("/Canvas/GameLayer/MapLayer").getComponent(MapLayer);
        this.tank = tank;
        this.tank.bulletCount--;
        this.isEnemy = tank instanceof EnemyTank;
        this.dir = dir;
        this.step = step;
        this.stopMoving = false;

        // 初始化位置
        this.node.position = pos;
        switch (this.dir) {
            case Dir.LEFT:
                this.node.x -= Globals.BULLET_SIZE;
                break;
            case Dir.RIGHT:
                this.node.x += Globals.BULLET_SIZE;
                break;
            case Dir.UP:
                this.node.y += Globals.BULLET_SIZE;
                break;
            case Dir.DOWN:
                this.node.y -= Globals.BULLET_SIZE;
                break;
            default:
                break;
        }

        this.getComponent(cc.Sprite).spriteFrame = this.frames[this.dir];
    }

    onBulletDestory() {
        this.stopMoving = true;
        this.tank.bulletCount++;
        this.mapLayer.destoryBullet(this.node);
    }

    protected update(dt: number): void {
        if (this.stopMoving) {
            return;
        }

        let realStep = this.step * 50 * dt;
        switch (this.dir) {
            case Dir.LEFT:
                this.node.x -= realStep;
                break;
            case Dir.RIGHT:
                this.node.x += realStep;
                break;
            case Dir.UP:
                this.node.y += realStep;
                break;
            case Dir.DOWN:
                this.node.y -= realStep;
                break;
            default:
                break;
        }

        if (this._isCollisionWithBlock() || this._isCollisionWithMap() || this._isCollisionWithTank()) {
            this._playAnimation();
        } else if (this._isCollisionWithBullet()) {
            this.onBulletDestory();
        }
    }

    _playAnimation() {
        this.stopMoving = true;
        this.tank.bulletCount++;
        this.getComponent(cc.Animation).play("bomb");
    }

    // bomb动画播放完后执行
    afterBombAnimationCompleted() {
        this.getComponent(cc.Animation).stop("bomb");
        this.mapLayer.destoryBullet(this.node);
    }

    _isCollisionWithMap() {
        let node = this.node;
        let offset = Globals.BULLET_SIZE / 2;
        if (node.x - offset < 0 || node.x + offset > Globals.MAP_WIDTH
            || node.y + offset > Globals.MAP_HEIGHT || node.y - offset < 0) {
            if (!this.isEnemy) {
                cc.find("/Game/AudioMgr").getComponent(AudioMgr).playAudio("bin", false);
            }
            return true;
        }
        return false;
    }

    _isCollisionWithBlock() {
        let count = 0;
        let blocks: cc.Node[] = this.mapLayer.blocksNode.children;
        let box = this.node.getBoundingBox();

        // 加宽子弹
        switch (this.dir) {
            case Dir.LEFT:
            case Dir.RIGHT:
                box = new cc.Rect(
                    box.xMin, 
                    box.yMin - Globals.BULLET_SIZE,
                    Globals.BULLET_SIZE,
                    3 * Globals.BULLET_SIZE
                );
                break;
            case Dir.UP:
            case Dir.DOWN:
                box = new cc.Rect(
                    box.xMin - Globals.BULLET_SIZE,
                    box.yMin,
                    3 * Globals.BULLET_SIZE,
                    Globals.BULLET_SIZE
                );
                break;
            default:
                break;
        }

        for (let i = 0; i < blocks.length; ++i) {
            let block = blocks[i];
            if (box.intersects(block.getBoundingBox())) {
                if (block.name == "block_wall") {
                    if (!this.isEnemy) {
                        cc.find("/Game/AudioMgr").getComponent(AudioMgr).playAudio("hit_wall", false);
                    }
                    if (block.getComponent(BlockWall).tryDestory(box, this.tank)) {
                        i--;
                    }
                    count++;
                } else if (block.name == "block_stone") {
                    if (!this.isEnemy) {
                        cc.find("/Game/AudioMgr").getComponent(AudioMgr).playAudio("hit_stone", false);
                        // 等级2级以上的坦克可以击穿冰块
                        if ((this.tank.level >= 2) && block.getComponent(BlockStone).tryDestory(box)) {
                            i--;
                        }
                    }
                    count++;
                } else if (block.name == "camp") {
                    block.getComponent(BlockCamp).tryDestory();
                    count++;
                }
            }
        }

        return count > 0;
    }

    _isCollisionWithTank() {
        let box = this.node.getBoundingBox();

        if (this.isEnemy) {
            let players = this.mapLayer.playerNode.children;
            for (const player of players) {
                if (box.intersects(player.getBoundingBox())) {
                    player.getComponent(PlayerTank).disBlood();
                    return true;
                }
            }
        } else {
            let enemies = this.mapLayer.enemiesNode.children;
            for (const enemy of enemies) {
                if (box.intersects(enemy.getBoundingBox())) {
                    enemy.getComponent(EnemyTank).disBlood();
                    return true;
                }
            }
        }
        return false;
    }

    _isCollisionWithBullet() {
        let box = this.node.getBoundingBox();

        // 只检查玩家的子弹
        if (!this.isEnemy) {
            let bullets = this.mapLayer.enemyBulletsNode.children;
            for (let i=0; i!=bullets.length; ++i) {
                if (box.intersects(bullets[i].getBoundingBox())) {
                    bullets[i].getComponent(Bullet).onBulletDestory();
                    return true;
                }
            }
        }

        return false;
    }
}
