const {ccclass, property} = cc._decorator;

@ccclass
export default class AudioMgr extends cc.Component {

    @property(cc.AudioClip)
    binAduio: cc.AudioClip = null;

    @property(cc.AudioClip)
    shootAduio: cc.AudioClip = null;

    @property(cc.AudioClip)
    playerMoveAudio: cc.AudioClip = null;

    @property(cc.AudioClip)
    tankBombAudio: cc.AudioClip = null;

    @property(cc.AudioClip)
    campBombAudio: cc.AudioClip = null;

    @property(cc.AudioClip)
    gameOverAudio: cc.AudioClip = null;

    @property(cc.AudioClip)
    gameStartAudio: cc.AudioClip = null;

    @property(cc.AudioClip)
    hitStone: cc.AudioClip = null; 

    @property(cc.AudioClip)
    hitWall: cc.AudioClip = null;

    @property(cc.AudioClip)
    propOut: cc.AudioClip = null;

    @property(cc.AudioClip)
    stopEnemy: cc.AudioClip = null; 

    @property(cc.AudioClip)
    addLife: cc.AudioClip = null;

    @property(cc.AudioClip)
    invincible: cc.AudioClip = null;

    @property(cc.AudioClip)
    reinforce: cc.AudioClip = null;

    @property(cc.AudioClip)
    bouns: cc.AudioClip = null;

    @property(cc.Boolean)
    enableAduio: boolean = true;

    playerMoveID: number;

    playAudio(name: string, loop = false) {
        if (!this.enableAduio) return;

        if (name == "bin") {
            cc.audioEngine.playEffect(this.binAduio, loop);
        } else if(name == "shoot") {
            cc.audioEngine.playEffect(this.shootAduio, loop);
        } else if (name == "player_move") {
            this.playerMoveID = cc.audioEngine.playEffect(this.playerMoveAudio, loop);
            cc.audioEngine.setVolume(this.playerMoveID, 0.8);
        } else if (name == "tank_bomb") {
            cc.audioEngine.playEffect(this.tankBombAudio, loop);
        } else if (name == "game_over") {
            cc.audioEngine.playEffect(this.gameOverAudio, loop);
        } else if (name == "game_start") {
            cc.audioEngine.playEffect(this.gameStartAudio, loop);
        } else if (name == "camp_bomb") {
            cc.audioEngine.playEffect(this.campBombAudio, loop);
        } else if (name == "hit_stone") {
            cc.audioEngine.playEffect(this.hitStone, loop);
        } else if (name == "hit_wall") {
            cc.audioEngine.playEffect(this.hitWall, loop);
        } else if (name == "prop_out") {
            cc.audioEngine.playEffect(this.propOut, loop);
        } else if (name == "stop_enemy") {
            cc.audioEngine.playEffect(this.stopEnemy, loop);
        } else if (name == "add_life") {
            cc.audioEngine.playEffect(this.addLife, loop);
        } else if (name == "invincible") {
            cc.audioEngine.playEffect(this.invincible, loop);
        } else if (name == "reinforce") {
            cc.audioEngine.playEffect(this.reinforce, loop);
        } else if (name == "bouns") {
            cc.audioEngine.playEffect(this.bouns, loop);
        }
    }

    stopAudio(name: string) {
        if (!this.enableAduio) return;

        if (name == "player_move") {
            cc.audioEngine.stop(this.playerMoveID);
        }
    }

}
