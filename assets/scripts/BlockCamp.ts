import Game from "./Game";
import AudioMgr from "./AudioMgr";
import { Globals } from "./Global";
 
const { ccclass, property } = cc._decorator;
 
@ccclass
export default class BlockCamp extends cc.Component {

    @property(cc.SpriteFrame)
    destoryed: cc.SpriteFrame = null;

    tryDestory() {
        this.getComponent(cc.Sprite).spriteFrame = this.destoryed;
        // 播放基地爆炸音效
        cc.find("/Game/AudioMgr").getComponent(AudioMgr).playAudio("camp_bomb");
        // 摧毁基地后播放上升的game over动画
        if (!Globals.FORCE_CONTINUE_GAME) {
            cc.find("/Game").getComponent(Game).gameOver();
        }
    }
}