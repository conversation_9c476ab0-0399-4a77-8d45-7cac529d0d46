const {ccclass} = cc._decorator;

@ccclass
export default class <PERSON><PERSON> extends cc.Component {
    _node: cc.Node = null;
    
    onLoad() {
        this._node = cc.find("/Canvas");

        cc.director.preloadScene("Game");   
        this._node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this, true);
    }

    // 触摸屏幕就开始游戏
    onTouchStart(event: cc.Event.EventTouch) {
        cc.director.loadScene("Game");
    }

    onDestroy() {
        this._node.off(cc.Node.EventType.TOUCH_START, this.onTouchStart, this, true);
    }
}
