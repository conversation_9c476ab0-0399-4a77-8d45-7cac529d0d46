import { Globals } from "./Global";

const {ccclass, property} = cc._decorator;

@ccclass
export default class UpdateInfomations extends cc.Component {

    @property(cc.Prefab)
    enemyIcon: cc.Prefab = null;
    @property(cc.Node)
    enemiesIcon: cc.Node = null;

    deleteOneIcon() {
        this.enemiesIcon.children[this.enemiesIcon.childrenCount - 1].destroy();
    }

    updatePlayerBlood(blood: number) {
        this.node.getChildByName("player_blood").getComponent(cc.Label).string = blood.toString();
    }

    init (level: number) {
        this.enemiesIcon.removeAllChildren(true);

        const col = 2;
        const row = Globals.ENEMIES_COUNT / col;

        for (let i = 0; i != row - 1; i++) {
            for (let j = 0; j != col; j++) {
                let node = cc.instantiate(this.enemyIcon);
                node.name = "icon";
                node.parent = this.enemiesIcon;
            }
        }

        this.node.getChildByName("cur_level").getComponent(cc.Label).string = level.toString();
    }
}
