export enum Dir {
    LEFT = 0,
    UP = 1,
    RIGHT = 2,
    DOWN = 3
};

export const Globals = {
    BLOCK_SIZE: 8,
    TANK_SIZE: 16,
    MAP_HEIGHT: 208,
    MAP_WIDTH: 208,
    BULLET_SIZE: 4,
    ENEMIES_COUNT: 20,

    // 敌方坦克少于该数量，则可以出现开始随机道具坦克
    PROP_APPEAR_REMAIN_COUNT: 20,
    // 每次道具坦克出现的权重
    PROP_APPEAR_WEIGHT: 100,
    // 敌方坦克攻击权重
    ENEMY_ATTACK_WEIGHT: 50,
    // 敌方坦克暂停时间（秒）
    ENEMY_STOP_SECS: 10,
    // 道具停留时间（秒）
    PROP_TIME: 20,
    // 基地加固持续时长
    REINFORCE_TIME: 20,
    // 强制继续游戏
    FORCE_CONTINUE_GAME: false,
    // 玩家无敌状态持续时间（秒）
    INVINCIBLE_TIME: 5,
    // 玩家坦克调试状态
    DEBUG_PLAYER_TANK: false,
 
    ENEMY1: cc.v2(8, 200),
    ENEMY2: cc.v2(104, 200),
    ENEMY3: cc.v2(200, 200),
    PLAYER1: cc.v2(80, 8),

    Z: {
        FOREST: cc.macro.MAX_ZINDEX,
        TANK: cc.macro.MAX_ZINDEX - 1,
        BULLET: cc.macro.MAX_ZINDEX - 2,
        OTHERS: cc.macro.MAX_ZINDEX - 3,
        ICE: cc.macro.MAX_ZINDEX - 4,
    },
};