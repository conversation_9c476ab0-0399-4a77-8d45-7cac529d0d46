#!/usr/bin/env python3
"""
预览替换后的飞机图片
"""

from PIL import Image
import os

def create_preview():
    """创建预览图片，显示所有飞机等级"""
    
    # 图片路径
    aircraft_dir = 'assets/textures/tank/player_tank'
    backup_dir = 'backup_tank_images'
    
    # 创建预览图片
    preview_width = 16 * 8 + 10 * 7  # 8张图片 + 间隔
    preview_height = 16 * 3 + 10 * 2  # 3行 + 间隔
    
    preview_img = Image.new('RGBA', (preview_width, preview_height), (50, 50, 50, 255))
    
    # 标题文字位置
    y_positions = [0, 26, 52]  # 三行的Y位置
    titles = ['原始坦克图片:', '新的飞机图片:', '对比效果:']
    
    # 第一行：原始坦克图片
    y = 0
    for i in range(4):  # 4个等级
        for frame in range(2):  # 2帧
            filename = f'm{i}-0-{frame+1}.png'
            tank_path = os.path.join(backup_dir, filename)
            
            if os.path.exists(tank_path):
                tank_img = Image.open(tank_path)
                x = (i * 2 + frame) * (16 + 10)
                preview_img.paste(tank_img, (x, y), tank_img)
    
    # 第二行：新的飞机图片
    y = 26
    for i in range(4):  # 4个等级
        for frame in range(2):  # 2帧
            filename = f'm{i}-0-{frame+1}.png'
            aircraft_path = os.path.join(aircraft_dir, filename)
            
            if os.path.exists(aircraft_path):
                aircraft_img = Image.open(aircraft_path)
                x = (i * 2 + frame) * (16 + 10)
                preview_img.paste(aircraft_img, (x, y), aircraft_img)
    
    # 第三行：放大对比
    y = 52
    scale = 1
    for i in range(4):  # 只显示每个等级的第一帧
        # 坦克图片
        tank_filename = f'm{i}-0-1.png'
        tank_path = os.path.join(backup_dir, tank_filename)
        
        # 飞机图片
        aircraft_filename = f'm{i}-0-1.png'
        aircraft_path = os.path.join(aircraft_dir, aircraft_filename)
        
        x_base = i * (16 * 2 + 20)
        
        if os.path.exists(tank_path):
            tank_img = Image.open(tank_path)
            preview_img.paste(tank_img, (x_base, y), tank_img)
        
        if os.path.exists(aircraft_path):
            aircraft_img = Image.open(aircraft_path)
            preview_img.paste(aircraft_img, (x_base + 16 + 4, y), aircraft_img)
    
    # 保存预览图片
    preview_img.save('aircraft_preview.png')
    print("预览图片已保存为: aircraft_preview.png")
    
    # 显示图片信息
    print("\n=== 替换完成 ===")
    print("✅ 原始坦克图片已备份到: backup_tank_images/")
    print("✅ 玩家坦克图片已替换为飞机图片")
    print("✅ 保持了原有的4个等级和动画帧")
    print("✅ 图片尺寸和格式保持一致 (16x16 PNG)")
    
    print("\n=== 文件对应关系 ===")
    levels = ['基础级别', '一级升级', '二级升级', '三级升级']
    for i in range(4):
        print(f"等级 {i} ({levels[i]}):")
        print(f"  - m{i}-0-1.png (动画帧1)")
        print(f"  - m{i}-0-2.png (动画帧2)")
    
    print("\n现在你可以在Cocos Creator中运行游戏，玩家坦克应该显示为飞机了！")

if __name__ == "__main__":
    create_preview()
